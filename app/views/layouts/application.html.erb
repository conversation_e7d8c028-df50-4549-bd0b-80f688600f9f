<!DOCTYPE html>
<html lang="en">
  <head>    
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <%= display_meta_tags(site: get_option("site_name", "RORSCHOOLS")) %>
    <%= csrf_meta_tags %>
    <%= csp_meta_tag %>

    <%= yield :head %>

    <%# Enable PWA manifest for installable apps (make sure to enable in config/routes.rb too!) %>
    <%#= tag.link rel: "manifest", href: pwa_manifest_path(format: :json) %>

    <link rel="icon" href="/icon.png" type="image/png">
    <link rel="icon" href="/icon.svg" type="image/svg+xml">
    <link rel="apple-touch-icon" href="/icon.png">

    <%# Includes all stylesheet files in app/assets/stylesheets %>
    <%= stylesheet_link_tag "application", "data-turbo-track": "reload" %>
    <%= javascript_include_tag "application", "data-turbo-track": "reload", type: "module" %>
    <script>
      (function() {        
        const lightTheme = "<%= @application_light_theme || get_option("site_light_theme", "silk") %>";
        const darkTheme = "<%= @application_dark_theme || get_option("site_dark_theme", "dark") %>";        
        const savedTheme = localStorage.getItem('theme');
        
        let effectiveTheme;
        if (savedTheme && (savedTheme === 'light' || savedTheme === 'dark')) {
          effectiveTheme = savedTheme === 'dark' ? darkTheme : lightTheme;
        } else if (savedTheme) {          
          effectiveTheme = savedTheme;
        } else {          
          effectiveTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? darkTheme : lightTheme;
        }

        document.documentElement.setAttribute('data-theme', effectiveTheme);
      })();
    </script>
  </head>

  <body>
    <main>
      <%= render partial: "partials/navbar" %>      
      <%= render partial: "partials/alerts" %>
      <%= yield %>      
    </main>
  </body>
</html>
